import { requireAuth } from '@/lib/server/auth';
import { DashboardStats } from '@/components/dashboard/DashboardStats';
import { WelcomeCard } from '@/components/dashboard/WelcomeCard';
import { QuickActions } from '@/components/dashboard/QuickActions';
import { RecentActivity } from '@/components/dashboard/RecentActivity';
import { mockDashboardData } from '@/lib/mock-data';

async function getDashboardData() {
  // Simulate API call - in a real app, this would fetch from your API
  await new Promise(resolve => setTimeout(resolve, 100));
  return mockDashboardData;
}

export default async function DashboardPage() {
  // Require authentication on server side
  const session = await requireAuth();

  // Fetch dashboard data on server side
  const dashboardData = await getDashboardData();

  const getUserDisplayName = () => {
    return session?.user?.name || session?.user?.email || 'Pengguna';
  };

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <WelcomeCard userName={getUserDisplayName()} />

      {/* Stats Cards */}
      <DashboardStats stats={dashboardData.stats} />

      {/* Main Content Grid */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Recent Activity */}
        <RecentActivity activities={dashboardData.recentActivities} />

        {/* Quick Actions */}
        <QuickActions actions={dashboardData.quickActions} />
      </div>
    </div>
  );
}
