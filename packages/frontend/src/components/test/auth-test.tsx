'use client';

import { useAuthenticatedApi } from '@/hooks/useAuthenticatedApi';
import { useSession, signIn, signOut } from 'next-auth/react';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { suppliersApi } from '@/lib/api/suppliers';
import { apiClient } from '@/lib/axios';

export function AuthTest() {
  const { isAuthenticated, isLoading } = useAuthenticatedApi();
  const { data: sessionData } = useSession();
  const [testResult, setTestResult] = useState<string>('');
  const [authHeaders, setAuthHeaders] = useState<string>('');

  useEffect(() => {
    // Check current auth headers
    const headers = apiClient.defaults.headers.common;
    setAuthHeaders(JSON.stringify(headers, null, 2));
  }, [sessionData]);

  const testApiCall = async () => {
    try {
      setTestResult('Testing API call...');
      console.log('Session data:', sessionData);
      console.log('API client headers:', apiClient.defaults.headers.common);

      const response = await suppliersApi.getStats();
      setTestResult(`Success! Stats: ${JSON.stringify(response, null, 2)}`);
    } catch (error: any) {
      console.error('API call error:', error);
      setTestResult(`Error: ${error.message} - ${JSON.stringify(error.response?.data || error, null, 2)}`);
    }
  };

  const testManualToken = async () => {
    try {
      setTestResult('Testing with manual token...');
      // Use the token from our previous test
      const manualToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.bWVt5ACAGVoull8QgmwdWGSkzYchPzhCkEWM2RKqczE';

      const response = await apiClient.get('/suppliers/stats', {
        headers: {
          'Authorization': `Bearer ${manualToken}`
        }
      });

      setTestResult(`Manual token success! Stats: ${JSON.stringify(response.data, null, 2)}`);
    } catch (error: any) {
      console.error('Manual token error:', error);
      setTestResult(`Manual token error: ${error.message} - ${JSON.stringify(error.response?.data || error, null, 2)}`);
    }
  };

  const handleLogin = async () => {
    try {
      setTestResult('Logging in...');
      const result = await signIn('credentials', {
        email: '<EMAIL>',
        password: 'password123',
        redirect: false,
      });

      if (result?.error) {
        setTestResult(`Login error: ${result.error}`);
      } else {
        setTestResult('Login successful! Check session data above.');
      }
    } catch (error: any) {
      setTestResult(`Login error: ${error.message}`);
    }
  };

  const handleLogout = async () => {
    await signOut({ redirect: false });
    setTestResult('Logged out successfully');
  };

  return (
    <Card className="max-w-2xl mx-auto mt-8">
      <CardHeader>
        <CardTitle>Authentication Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <strong>Auth Status:</strong>
          <ul className="ml-4 mt-2">
            <li>Is Loading: {isLoading ? 'Yes' : 'No'}</li>
            <li>Is Authenticated: {isAuthenticated ? 'Yes' : 'No'}</li>
            <li>Session exists: {sessionData ? 'Yes' : 'No'}</li>
            <li>Access Token: {sessionData?.accessToken ? 'Present' : 'Missing'}</li>
          </ul>
        </div>

        {sessionData && (
          <div>
            <strong>Session Data:</strong>
            <pre className="bg-gray-100 p-2 rounded text-xs mt-2 overflow-auto">
              {JSON.stringify(sessionData, null, 2)}
            </pre>
          </div>
        )}

        <div>
          <strong>Current API Headers:</strong>
          <pre className="bg-gray-100 p-2 rounded text-xs mt-2 overflow-auto">
            {authHeaders}
          </pre>
        </div>

        <div className="flex flex-wrap gap-2">
          {!isAuthenticated ? (
            <Button onClick={handleLogin}>
              Login (<EMAIL>)
            </Button>
          ) : (
            <Button onClick={handleLogout} variant="outline">
              Logout
            </Button>
          )}
          <Button onClick={testApiCall} disabled={!isAuthenticated}>
            Test API Call
          </Button>
          <Button onClick={testManualToken} variant="outline">
            Test Manual Token
          </Button>
        </div>

        {testResult && (
          <div>
            <strong>Test Result:</strong>
            <pre className="bg-gray-100 p-2 rounded text-xs mt-2 overflow-auto">
              {testResult}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
