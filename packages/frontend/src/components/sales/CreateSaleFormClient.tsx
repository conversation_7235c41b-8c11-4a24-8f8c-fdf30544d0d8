'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { CustomerSelector } from '@/components/ui/customer-selector';
import { ProductSelector } from '@/components/ui/product-selector';
import { UnitSelector } from '@/components/ui/unit-selector';
import { LiveCurrencyInput } from '@/components/ui/currency-input';
import { CreateSaleDto, PaymentMethod } from '@/types/sales';
import { useCreateSale, useCreateDraft, useGenerateSaleNumber } from '@/hooks/useSales';
import { useProduct } from '@/hooks/useProducts';
import { formatCurrency } from '@/lib/utils';
import {
  Plus,
  Trash2,
  Save,
  CreditCard,
  Calculator,
  User,
  ShoppingCart,
  Receipt,
  ArrowLeft
} from 'lucide-react';
import { toast } from 'sonner';

// Form validation schema
const saleItemSchema = z.object({
  productId: z.string().min(1, 'Produk harus dipilih'),
  unitId: z.string().min(1, 'Satuan harus dipilih'),
  quantity: z.number().min(1, 'Jumlah minimal 1'),
  unitPrice: z.number().min(0, 'Harga tidak boleh negatif'),
  discountType: z.enum(['PERCENTAGE', 'FIXED_AMOUNT']).optional(),
  discountValue: z.number().min(0).optional(),
  notes: z.string().optional(),
});

const createSaleSchema = z.object({
  saleNumber: z.string().optional(),
  customerId: z.string().optional(),
  customerName: z.string().optional(),
  customerPhone: z.string().optional(),
  paymentMethod: z.nativeEnum(PaymentMethod),
  amountPaid: z.number().min(0, 'Jumlah bayar tidak boleh negatif'),
  discountType: z.enum(['PERCENTAGE', 'FIXED_AMOUNT']).optional(),
  discountValue: z.number().min(0).optional(),
  taxAmount: z.number().min(0).optional(),
  notes: z.string().optional(),
  items: z.array(saleItemSchema).min(1, 'Minimal harus ada 1 item'),
});

type CreateSaleFormData = z.infer<typeof createSaleSchema>;

const PAYMENT_METHOD_OPTIONS = [
  { value: PaymentMethod.CASH, label: 'Tunai' },
  { value: PaymentMethod.TRANSFER, label: 'Transfer Bank' },
  { value: PaymentMethod.CREDIT, label: 'Kredit' },
  { value: PaymentMethod.GIRO, label: 'Giro' },
];

export function CreateSaleFormClient() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [submitAction, setSubmitAction] = useState<'draft' | 'complete'>('draft');

  // Mutations
  const createSaleMutation = useCreateSale();
  const createDraftMutation = useCreateDraft();
  const { refetch: generateSaleNumber } = useGenerateSaleNumber();

  // Form setup
  const form = useForm<CreateSaleFormData>({
    resolver: zodResolver(createSaleSchema),
    defaultValues: {
      saleNumber: '',
      customerId: '',
      customerName: '',
      customerPhone: '',
      paymentMethod: PaymentMethod.CASH,
      amountPaid: 0,
      discountType: undefined,
      discountValue: 0,
      taxAmount: 0,
      notes: '',
      items: [],
    },
  });

  const { fields: itemFields, append: appendItem, remove: removeItem } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  // Watch form values for calculations
  const watchedItems = form.watch('items');
  const watchedDiscountType = form.watch('discountType');
  const watchedDiscountValue = form.watch('discountValue');
  const watchedTaxAmount = form.watch('taxAmount');
  const watchedAmountPaid = form.watch('amountPaid');

  // Calculate totals
  const calculations = useCallback(() => {
    const itemsSubtotal = watchedItems.reduce((sum, item) => {
      const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);
      const itemDiscount = item.discountType === 'PERCENTAGE'
        ? itemTotal * ((item.discountValue || 0) / 100)
        : (item.discountValue || 0);
      return sum + itemTotal - itemDiscount;
    }, 0);

    const saleDiscount = watchedDiscountType === 'PERCENTAGE'
      ? itemsSubtotal * ((watchedDiscountValue || 0) / 100)
      : (watchedDiscountValue || 0);

    const subtotalAfterDiscount = itemsSubtotal - saleDiscount;
    const taxAmount = watchedTaxAmount || 0;
    const totalAmount = subtotalAfterDiscount + taxAmount;
    const changeAmount = Math.max(0, (watchedAmountPaid || 0) - totalAmount);

    return {
      itemsSubtotal,
      saleDiscount,
      subtotalAfterDiscount,
      taxAmount,
      totalAmount,
      changeAmount,
    };
  }, [watchedItems, watchedDiscountType, watchedDiscountValue, watchedTaxAmount, watchedAmountPaid]);

  const totals = calculations();

  // Generate sale number on component mount
  useEffect(() => {
    const generateNumber = async () => {
      try {
        const result = await generateSaleNumber();
        if (result.data?.saleNumber) {
          form.setValue('saleNumber', result.data.saleNumber);
        }
      } catch (error) {
        console.error('Failed to generate sale number:', error);
      }
    };

    generateNumber();
  }, [generateSaleNumber, form]);

  // Add new item
  const handleAddItem = () => {
    appendItem({
      productId: '',
      unitId: '',
      quantity: 1,
      unitPrice: 0,
      discountType: 'PERCENTAGE', // Set a default value instead of undefined
      discountValue: 0,
      notes: '',
    });
  };

  // Remove item
  const handleRemoveItem = (index: number) => {
    removeItem(index);
  };



  // Handle form submission
  const handleSubmit = async (action: 'draft' | 'complete') => {
    setSubmitAction(action);
    setShowConfirmDialog(true);
  };

  const confirmSubmit = async () => {
    setIsSubmitting(true);
    setShowConfirmDialog(false);

    try {
      const formData = form.getValues();

      // Prepare the sale data
      const saleData: CreateSaleDto = {
        ...formData,
        // Handle walk-in customer: convert 'walk-in' to undefined for backend
        customerId: formData.customerId === 'walk-in' ? undefined : formData.customerId,
        items: formData.items.map(item => ({
          productId: item.productId,
          unitId: item.unitId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discountType: item.discountType,
          discountValue: item.discountValue,
          notes: item.notes,
        })),
      };

      // Create the sale using the appropriate mutation
      if (submitAction === 'draft') {
        await createDraftMutation.mutateAsync(saleData);
        toast.success('Draft transaksi berhasil disimpan');
      } else {
        // For complete action, create as completed sale directly
        await createSaleMutation.mutateAsync(saleData);
        toast.success('Transaksi berhasil diselesaikan');
      }

      // Redirect to sales list
      router.push('/dashboard/sales');
    } catch (error) {
      console.error('Failed to create sale:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form className="space-y-6">
          {/* Header Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Receipt className="h-5 w-5" />
                Informasi Transaksi
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="saleNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nomor Transaksi</FormLabel>
                      <FormControl>
                        <Input
                          value={field.value || ''}
                          onChange={field.onChange}
                          disabled
                          placeholder="Auto-generated"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Customer Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Informasi Pelanggan
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="customerId"
                render={({ field }) => {

                  const handleChange = (customerId: string | undefined) => {
                    // Update the field value directly
                    field.onChange(customerId);

                    // Handle additional logic for walk-in customers
                    if (customerId === 'walk-in') {
                      form.setValue('customerName', '');
                      form.setValue('customerPhone', '');
                    } else if (!customerId) {
                      form.setValue('customerName', '');
                      form.setValue('customerPhone', '');
                    } else {
                      // For registered customers, clear walk-in fields
                      form.setValue('customerName', '');
                      form.setValue('customerPhone', '');
                    }
                  };

                  return (
                    <FormItem>
                      <FormLabel>Pelanggan</FormLabel>
                      <FormControl>
                        <CustomerSelector
                          value={field.value}
                          onValueChange={handleChange}
                          placeholder="Pilih pelanggan atau gunakan pelanggan umum"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />

              {/* Walk-in customer fields */}
              {(form.watch('customerId') === 'walk-in' || form.watch('customerId') === '') && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="customerName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nama Pelanggan</FormLabel>
                        <FormControl>
                          <Input
                            value={field.value || ''}
                            onChange={field.onChange}
                            placeholder="Nama pelanggan (opsional)"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="customerPhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nomor Telepon</FormLabel>
                        <FormControl>
                          <Input
                            value={field.value || ''}
                            onChange={field.onChange}
                            placeholder="Nomor telepon (opsional)"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Items Card */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <ShoppingCart className="h-5 w-5" />
                  Item Transaksi
                </CardTitle>
                <Button type="button" onClick={handleAddItem} size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Item
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {itemFields.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <ShoppingCart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Belum ada item yang ditambahkan</p>
                  <p className="text-sm">Klik "Tambah Item" untuk memulai</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {itemFields.map((field, index) => (
                    <SaleItemRow
                      key={field.id}
                      index={index}
                      form={form}
                      onRemove={() => handleRemoveItem(index)}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Totals Card */}
          {itemFields.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  Ringkasan Pembayaran
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Sale-level discount */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="discountType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Jenis Diskon</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Pilih jenis diskon" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="PERCENTAGE">Persentase (%)</SelectItem>
                            <SelectItem value="FIXED_AMOUNT">Nominal (Rp)</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="discountValue"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nilai Diskon</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            value={field.value || 0}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            placeholder="0"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="taxAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Pajak (PPN)</FormLabel>
                        <FormControl>
                          <LiveCurrencyInput
                            value={field.value}
                            onValueChange={field.onChange}
                            placeholder="0"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Totals summary */}
                <Separator />
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal Item:</span>
                    <span>{formatCurrency(totals.itemsSubtotal)}</span>
                  </div>
                  {totals.saleDiscount > 0 && (
                    <div className="flex justify-between text-sm text-red-600">
                      <span>Diskon Penjualan:</span>
                      <span>-{formatCurrency(totals.saleDiscount)}</span>
                    </div>
                  )}
                  {totals.taxAmount > 0 && (
                    <div className="flex justify-between text-sm">
                      <span>Pajak (PPN):</span>
                      <span>{formatCurrency(totals.taxAmount)}</span>
                    </div>
                  )}
                  <Separator />
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total:</span>
                    <span>{formatCurrency(totals.totalAmount)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Payment Card */}
          {itemFields.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Pembayaran
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="paymentMethod"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Metode Pembayaran *</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Pilih metode pembayaran" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {PAYMENT_METHOD_OPTIONS.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="amountPaid"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Jumlah Bayar *</FormLabel>
                        <FormControl>
                          <LiveCurrencyInput
                            value={field.value}
                            onValueChange={field.onChange}
                            placeholder="0"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Change amount */}
                {totals.changeAmount > 0 && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="font-medium text-green-800">Kembalian:</span>
                      <span className="font-bold text-green-800 text-lg">
                        {formatCurrency(totals.changeAmount)}
                      </span>
                    </div>
                  </div>
                )}

                {/* Insufficient payment warning */}
                {watchedAmountPaid > 0 && watchedAmountPaid < totals.totalAmount && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="font-medium text-red-800">Kurang Bayar:</span>
                      <span className="font-bold text-red-800 text-lg">
                        {formatCurrency(totals.totalAmount - watchedAmountPaid)}
                      </span>
                    </div>
                  </div>
                )}

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Catatan</FormLabel>
                      <FormControl>
                        <Textarea
                          value={field.value || ''}
                          onChange={field.onChange}
                          placeholder="Catatan tambahan untuk transaksi ini..."
                          rows={3}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/dashboard/sales')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Kembali
            </Button>

            {itemFields.length > 0 && (
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => handleSubmit('draft')}
                  disabled={isSubmitting}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  Simpan Draft
                </Button>

                <Button
                  type="button"
                  onClick={() => handleSubmit('complete')}
                  disabled={isSubmitting || watchedAmountPaid < totals.totalAmount}
                  className="flex items-center gap-2"
                >
                  <Receipt className="h-4 w-4" />
                  Selesaikan Transaksi
                </Button>
              </div>
            )}
          </div>
        </form>
      </Form>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {submitAction === 'complete' ? 'Selesaikan Transaksi' : 'Simpan Draft'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {submitAction === 'complete'
                ? 'Apakah Anda yakin ingin menyelesaikan transaksi ini? Stok akan dikurangi dan transaksi tidak dapat diubah.'
                : 'Apakah Anda yakin ingin menyimpan transaksi sebagai draft?'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>
              Batal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmSubmit}
              disabled={isSubmitting}
              className={submitAction === 'complete' ? 'bg-primary hover:bg-primary/90' : 'bg-blue-600 hover:bg-blue-700'}
            >
              {isSubmitting
                ? (submitAction === 'complete' ? 'Menyelesaikan...' : 'Menyimpan...')
                : (submitAction === 'complete' ? 'Selesaikan' : 'Simpan')
              }
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

// Sale Item Row Component
function SaleItemRow({
  index,
  form,
  onRemove
}: {
  index: number;
  form: any;
  onRemove: () => void;
}) {
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [availableUnits, setAvailableUnits] = useState<any[]>([]);

  // Watch product selection
  const watchedProductId = form.watch(`items.${index}.productId`);
  const watchedUnitId = form.watch(`items.${index}.unitId`);
  const watchedQuantity = form.watch(`items.${index}.quantity`);
  const watchedUnitPrice = form.watch(`items.${index}.unitPrice`);
  const watchedDiscountType = form.watch(`items.${index}.discountType`);
  const watchedDiscountValue = form.watch(`items.${index}.discountValue`);

  // Fetch product details when product is selected
  const { data: productData } = useProduct(watchedProductId);

  // Update available units when product changes
  useEffect(() => {
    if (productData?.unitHierarchies) {
      // Transform unit hierarchies to unit format for the selector
      const units = productData.unitHierarchies.map((hierarchy: any) => ({
        id: hierarchy.unitId,
        name: hierarchy.unit.name,
        abbreviation: hierarchy.unit.abbreviation,
        conversionFactor: hierarchy.conversionFactor,
        sellingPrice: hierarchy.sellingPrice,
        level: hierarchy.level,
        isBaseUnit: hierarchy.level === 0,
      }));

      setAvailableUnits(units);

      // Reset unit selection when product changes
      if (watchedProductId && !watchedUnitId) {
        // Auto-select base unit if available
        const baseUnit = units.find((unit: any) => unit.isBaseUnit);
        if (baseUnit) {
          form.setValue(`items.${index}.unitId`, baseUnit.id);
          form.setValue(`items.${index}.unitPrice`, baseUnit.sellingPrice || 0);
        }
      }
    }
  }, [productData, watchedProductId, watchedUnitId, form, index]);

  // Update product data when product is selected
  useEffect(() => {
    if (productData) {
      setSelectedProduct(productData);
    }
  }, [productData]);

  // Update unit price when unit changes
  useEffect(() => {
    if (watchedUnitId && availableUnits.length > 0) {
      const selectedUnit = availableUnits.find((unit: any) => unit.id === watchedUnitId);
      if (selectedUnit?.sellingPrice) {
        form.setValue(`items.${index}.unitPrice`, selectedUnit.sellingPrice);
      }
    }
  }, [watchedUnitId, availableUnits, form, index]);

  // Calculate item totals
  const itemSubtotal = (watchedQuantity || 0) * (watchedUnitPrice || 0);
  const itemDiscount = watchedDiscountType === 'PERCENTAGE'
    ? itemSubtotal * ((watchedDiscountValue || 0) / 100)
    : (watchedDiscountValue || 0);
  const itemTotal = itemSubtotal - itemDiscount;

  return (
    <div className="border rounded-lg p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium">Item #{index + 1}</h4>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onRemove}
          className="text-destructive hover:text-destructive"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>

      {/* Product and Unit Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name={`items.${index}.productId`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Produk *</FormLabel>
              <FormControl>
                <ProductSelector
                  value={field.value}
                  onValueChange={(value) => {
                    field.onChange(value);
                    // Reset unit and price when product changes
                    form.setValue(`items.${index}.unitId`, '');
                    form.setValue(`items.${index}.unitPrice`, 0);
                  }}
                  placeholder="Pilih produk..."
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={`items.${index}.unitId`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Satuan *</FormLabel>
              <FormControl>
                <UnitSelector
                  units={availableUnits}
                  value={field.value}
                  onValueChange={field.onChange}
                  placeholder="Pilih satuan..."
                  disabled={!watchedProductId}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Quantity and Price */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name={`items.${index}.quantity`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Jumlah *</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="1"
                  step="1"
                  value={field.value || 1}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                  placeholder="1"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={`items.${index}.unitPrice`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Harga Satuan *</FormLabel>
              <FormControl>
                <LiveCurrencyInput
                  value={field.value}
                  onValueChange={field.onChange}
                  placeholder="0"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-2">
          <FormLabel>Subtotal</FormLabel>
          <div className="h-9 px-3 py-2 border rounded-md bg-muted flex items-center">
            <span className="font-medium">{formatCurrency(itemSubtotal)}</span>
          </div>
        </div>
      </div>

      {/* Item Discount */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name={`items.${index}.discountType`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Jenis Diskon</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih jenis diskon" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="PERCENTAGE">Persentase (%)</SelectItem>
                  <SelectItem value="FIXED_AMOUNT">Nominal (Rp)</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={`items.${index}.discountValue`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nilai Diskon</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  value={field.value || 0}
                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                  placeholder="0"
                  disabled={!watchedDiscountType}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-2">
          <FormLabel>Total Item</FormLabel>
          <div className="h-9 px-3 py-2 border rounded-md bg-primary/5 flex items-center">
            <span className="font-bold text-primary">{formatCurrency(itemTotal)}</span>
          </div>
        </div>
      </div>

      {/* Notes */}
      <FormField
        control={form.control}
        name={`items.${index}.notes`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Catatan Item</FormLabel>
            <FormControl>
              <Textarea
                value={field.value || ''}
                onChange={field.onChange}
                placeholder="Catatan untuk item ini..."
                rows={2}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Product Info Display */}
      {selectedProduct && (
        <div className="p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-2 text-sm">
            <Badge variant="outline">{selectedProduct.code}</Badge>
            <span className="font-medium">{selectedProduct.name}</span>
            {selectedProduct.manufacturer && (
              <span className="text-muted-foreground">• {selectedProduct.manufacturer}</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
