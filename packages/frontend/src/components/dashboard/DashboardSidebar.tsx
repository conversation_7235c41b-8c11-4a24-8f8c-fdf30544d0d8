'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import {
  Home,
  Pill,
  Package,
  ShoppingCart,
  Users,
  Building2,
  DollarSign,
  Settings,
  X,
} from 'lucide-react';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  current?: boolean;
}

const navigation: NavigationItem[] = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },
  { name: 'Manajemen Produk', href: '/dashboard/products', icon: Pill },
  { name: 'Inventori', href: '/dashboard/inventory', icon: Package },
  { name: 'Penjualan', href: '/dashboard/sales', icon: ShoppingCart },
  { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/dashboard/customers', icon: Users },
  { name: '<PERSON><PERSON>so<PERSON>', href: '/dashboard/suppliers', icon: Building2 },
  { name: '<PERSON><PERSON><PERSON>', href: '/dashboard/finance', icon: DollarSign },
  { name: 'Pengaturan', href: '/dashboard/settings', icon: Settings },
];

interface DashboardSidebarProps {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
}

export function DashboardSidebar({ sidebarOpen, setSidebarOpen }: DashboardSidebarProps) {
  const pathname = usePathname();
  const [pharmacyName, setPharmacyName] = useState('Apotek App');

  useEffect(() => {
    // Fetch pharmacy settings
    const fetchPharmacySettings = async () => {
      try {
        // This will be implemented when we have the API integration
        // For now, use default name
        setPharmacyName('Apotek Sehat Bersama');
      } catch (error) {
        console.error('Failed to fetch pharmacy settings:', error);
      }
    };

    fetchPharmacySettings();
  }, []);

  const SidebarContent = () => (
    <div className="flex flex-col flex-grow bg-card border-r pt-5 pb-4 overflow-y-auto">
      <div className="flex items-center flex-shrink-0 px-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="h-8 w-8 bg-primary rounded-lg flex items-center justify-center">
              <Pill className="h-5 w-5 text-primary-foreground" />
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-foreground">{pharmacyName}</p>
          </div>
        </div>
      </div>
      <div className="mt-8 flex-grow flex flex-col">
        <nav className="flex-1 px-2 space-y-1">
          {navigation.map((item) => {
            const isActive = pathname === item.href ||
              (item.href !== '/dashboard' && pathname.startsWith(item.href));

            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors',
                  isActive
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                )}
                onClick={() => setSidebarOpen(false)}
              >
                <item.icon
                  className={cn(
                    'mr-3 flex-shrink-0 h-5 w-5',
                    isActive ? 'text-primary-foreground' : 'text-muted-foreground'
                  )}
                />
                {item.name}
              </Link>
            );
          })}
        </nav>
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile sidebar */}
      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <SheetContent side="left" className="p-0 w-80">
          <SidebarContent />
        </SheetContent>
      </Sheet>

      {/* Desktop sidebar */}
      <div className="hidden lg:flex lg:w-80 lg:flex-col lg:fixed lg:inset-y-0">
        <SidebarContent />
      </div>
    </>
  );
}
