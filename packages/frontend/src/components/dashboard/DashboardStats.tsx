'use client';

import { <PERSON>, <PERSON>Content, <PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatCurrency, formatNumber } from '@/lib/utils';
import { DashboardStats as StatsType } from '@/types/Dashboard';
import {
  DollarSign,
  AlertTriangle,
  Clock,
  Users,
} from 'lucide-react';

interface DashboardStatsProps {
  stats: StatsType;
}

export function DashboardStats({ stats }: DashboardStatsProps) {
  const statsCards = [
    {
      title: 'Penjualan Hari Ini',
      value: formatCurrency(stats.dailySales),
      icon: DollarSign,
      description: 'Total penjualan hari ini',
      trend: '+12% dari kemarin',
      trendColor: 'text-green-600',
    },
    {
      title: 'Stok Menipis',
      value: formatNumber(stats.lowStock),
      icon: AlertTriangle,
      description: 'Obat yang perlu restok',
      badge: stats.lowStock > 10 ? 'destructive' : 'secondary',
    },
    {
      title: '<PERSON><PERSON><PERSON>',
      value: formatNumber(stats.pendingOrders),
      icon: Clock,
      description: '<PERSON><PERSON><PERSON> menunggu proses',
      badge: stats.pendingOrders > 0 ? 'secondary' : 'default',
    },
    {
      title: 'Pelanggan Aktif',
      value: formatNumber(stats.activeCustomers),
      icon: Users,
      description: 'Pelanggan bulan ini',
      trend: '+8% dari bulan lalu',
      trendColor: 'text-green-600',
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {statsCards.map((card, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {card.title}
            </CardTitle>
            <card.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{card.value}</div>
              {card.badge && (
                <Badge variant={card.badge as any}>
                  {card.value}
                </Badge>
              )}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {card.description}
            </p>
            {card.trend && (
              <p className={`text-xs mt-1 ${card.trendColor}`}>
                {card.trend}
              </p>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
