---
trigger: always_on
---

- always check existing class/functions/enums/types and use it, dont try to be redundant
- use bun as package manager
- dont use npx, npm, pnpm
- do not redundant
- Use indonesian for UI text such as headers, label, pharagraph, title, etc
- Use english for table name, code, comments, and other things for codes
- Always check port 3000 and 3001, if its LISTEN, so that i was already started the server (pnpm run dev, pnpm dev)
- Beware with routes order in NestJS
- Use your diagnostics tools every finished editing files